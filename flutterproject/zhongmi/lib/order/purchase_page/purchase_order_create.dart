import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';

// 订单进行中页面
const Color _greyBorderColor = Color.fromARGB(255, 185, 185, 185);
const Color _purpleColor = Color(0xFF7B68EE);
const Color _backgroundColor = Colors.white;
const Color _lightShadowColor = Color(0x1A9E9E9E); 
const Color _mediumShadowColor = Color(0x339E9E9E); 

class PurchaseOrderPage extends StatefulWidget {
  const PurchaseOrderPage({Key? key}) : super(key: key);

  @override
  State<PurchaseOrderPage> createState() => _PurchaseOrderPageState();
}

class _PurchaseOrderPageState extends State<PurchaseOrderPage> {
  final PageController _pageController = PageController();
  List<String> _uploadedImages = []; // 使用字符串模拟图片路径
  int _currentPageIndex = 0;

  @override
  Widget build(BuildContext context) {
    // 设置状态栏样式
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    return Scaffold(
      backgroundColor: _backgroundColor,
      appBar: _buildAppBar(context),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 16),
                  _buildUploadSection(),
                  const SizedBox(height: 20),
                  _buildOrderInfo(),
                  const SizedBox(height: 16),
                  _buildPriceCards(),
                  const SizedBox(height: 16),
                  _buildAdditionalInfo(),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
          _buildBottomSection(),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0.5,
      centerTitle: true,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, size: 20),
        color: Colors.black87,
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: const Text(
        '代买',
        style: TextStyle(
          color: Colors.black87,
          fontSize: 17,
          fontWeight: FontWeight.w500,
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            // TODO: 实现参考价格功能
          },
          child: const Text(
            '参考价格',
            style: TextStyle(
              color: Colors.black54,
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  // 模拟选择图片功能
  void _simulatePickImage() {
    // 模拟添加图片（实际应用中将连接到image_picker）
    setState(() {
      _uploadedImages.add('https://picsum.photos/id/${_uploadedImages.length + 10}/400/300');
    });
    
    // 跳转到新添加的图片
    if (_uploadedImages.length > 1) {
      _pageController.animateToPage(
        _uploadedImages.length - 1,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
    
    // 显示模拟提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已添加图片 ${_uploadedImages.length}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  // 删除图片
  void _removeImage(int index) {
    setState(() {
      _uploadedImages.removeAt(index);
      if (_currentPageIndex >= _uploadedImages.length && _uploadedImages.isNotEmpty) {
        _currentPageIndex = _uploadedImages.length - 1;
        _pageController.animateToPage(
          _currentPageIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  Widget _buildUploadSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      height: 160,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _greyBorderColor,
          width: 1,
          style: BorderStyle.solid,
        ),
      ),
      child: _uploadedImages.isEmpty
          ? _buildEmptyUploadWidget()
          : _buildImageCarousel(),
    );
  }

  // 空状态的上传组件
  Widget _buildEmptyUploadWidget() {
    return CustomPaint(
      painter: DashedBorderPainter(),
      child: InkWell(
        onTap: _simulatePickImage,
        borderRadius: BorderRadius.circular(8),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.insert_drive_file_outlined,
                size: 40,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 8),
              Text(
                '上传商品照片',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '点击选择图片',
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 图片轮播组件
  Widget _buildImageCarousel() {
    // 计算总页面数（图片数量 + 1个添加页面）
    final totalPages = _uploadedImages.length + 1;
    
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPageIndex = index;
              });
            },
            itemCount: totalPages,
            itemBuilder: (context, index) {
              // 最后一页是添加图片页面
              if (index == _uploadedImages.length) {
                return _buildAddImagePage();
              }
              // 显示上传的图片
              return _buildImagePage(index);
            },
          ),
          // 页面指示器
          if (totalPages > 1)
            Positioned(
              bottom: 12,
              left: 0,
              right: 0,
              child: _buildPageIndicator(totalPages),
            ),
          // 删除按钮（仅在图片页面显示）
          if (_currentPageIndex < _uploadedImages.length)
            Positioned(
              top: 8,
              right: 8,
              child: _buildDeleteButton(),
            ),
        ],
      ),
    );
  }

  // 图片页面（模拟显示）
  Widget _buildImagePage(int index) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        image: DecorationImage(
          image: NetworkImage(_uploadedImages[index]),
          fit: BoxFit.cover,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withOpacity(0.3),
            ],
          ),
        ),
        child: Align(
          alignment: Alignment.bottomLeft,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Text(
              '图片 ${index + 1}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 添加图片页面
  Widget _buildAddImagePage() {
    return InkWell(
      onTap: _simulatePickImage,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.grey[50],
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.add_a_photo_outlined,
                  size: 24,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '添加图片',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 页面指示器
  Widget _buildPageIndicator(int totalPages) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(totalPages, (index) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 3),
          width: _currentPageIndex == index ? 16 : 6,
          height: 6,
          decoration: BoxDecoration(
            color: _currentPageIndex == index 
                ? _purpleColor 
                : Colors.white.withOpacity(0.5),
            borderRadius: BorderRadius.circular(3),
          ),
        );
      }),
    );
  }

  // 删除按钮
  Widget _buildDeleteButton() {
    return GestureDetector(
      onTap: () => _removeImage(_currentPageIndex),
      child: Container(
        width: 28,
        height: 28,
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.6),
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.close,
          color: Colors.white,
          size: 16,
        ),
      ),
    );
  }

  Widget _buildOrderInfo() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildInfoRow('物品信息', '小龙虾'),
          _buildDivider(),
          _buildInfoRow('指定商户', '店铺A'),
          _buildDivider(),
          _buildInfoRow('送货地址', '某某街道', action: '地址库'),
          _buildDivider(),
          _buildInfoRow('送货时间', '立即', action: '预约'),
          _buildDivider(),
          _buildTimeSelector(),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {String? action}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      child: Row(
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          const SizedBox(width: 24),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.black87,
                fontSize: 14,
              ),
            ),
          ),
          if (action != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                border: Border.all(color: _greyBorderColor),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                action,
                style: const TextStyle(
                  color: Colors.black54,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTimeSelector() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      child: Row(
        children: [
          Text(
            '送货限时',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          const SizedBox(width: 24),
          Expanded(
            child: Row(
              children: [
                _buildTimeInput('0'),
                const SizedBox(width: 8),
                const Text('小时', style: TextStyle(fontSize: 14)),
                const SizedBox(width: 16),
                _buildTimeInput('30'),
                const SizedBox(width: 8),
                const Text('分钟', style: TextStyle(fontSize: 14)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeInput(String value) {
    return Container(
      width: 40,
      height: 28,
      decoration: BoxDecoration(
        border: Border.all(color: _greyBorderColor),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Center(
        child: Text(
          value,
          style: const TextStyle(fontSize: 14),
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return const Divider(
      height: 1,
      thickness: 0.5,
      indent: 16,
      endIndent: 16,
    );
  }

  Widget _buildPriceCards() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(child: _buildPriceCard('物品价值', '10000', '服务费')),
          const SizedBox(width: 12),
          Expanded(child: _buildPriceCard('服务费', '10000', '')),
          const SizedBox(width: 12),
          Expanded(child: _buildPriceCard('Staff保证金', '10000', '')),
        ],
      ),
    );
  }

  Widget _buildPriceCard(String title, String value, String subtitle) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: _lightShadowColor,
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                value,
                style: TextStyle(
                  color: _purpleColor,
                  fontSize: 28,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                'MOP',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildAdditionalInfoRow('体积', '3 cm³'),
          _buildDivider(),
          _buildAdditionalInfoRow('物品重量', '0-2 KG'),
          _buildDivider(),
          _buildAdditionalInfoRow('优惠券', '优惠券更划算', hasArrow: true),
          _buildProgressIndicator(),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoRow(String label, String value, {bool hasArrow = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      child: Row(
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 14,
            ),
          ),
          if (hasArrow)
            const Icon(
              Icons.arrow_forward_ios,
              size: 14,
              color: Colors.grey,
            ),
        ],
      ),
    );
  }

  Widget _buildBottomSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: _mediumShadowColor,
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          children: [
        
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: () {
                        // TODO: 实现支付功能
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _purpleColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(24),
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        '支付并下单',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '责任声明',
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 12,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final steps = [
      {'icon': Icons.assignment, 'label': 'BOSS已下单', 'active': true},
      {'icon': Icons.check_circle_outline, 'label': 'Staff已接单', 'active': false},
      {'icon': Icons.shopping_bag_outlined, 'label': 'Staff已买货品', 'active': false},
      {'icon': Icons.description_outlined, 'label': '完成订单', 'active': false},
    ];

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: steps.map((step) {
          final isActive = step['active'] as bool;
          return Column(
            children: [
              Icon(
                step['icon'] as IconData,
                size: 24,
                color: isActive ? _purpleColor : Colors.grey[400],
              ),
              const SizedBox(height: 4),
              Text(
                step['label'] as String,
                style: TextStyle(
                  fontSize: 10,
                  color: isActive ? _purpleColor : Colors.grey[500],
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }
}

// 自定义虚线边框画笔
class DashedBorderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = _greyBorderColor
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(8, 8, size.width - 16, size.height - 16),
        const Radius.circular(4),
      ));

    final dashWidth = 5.0;
    final dashSpace = 3.0;
    final pathMetrics = path.computeMetrics();
    
    for (final metric in pathMetrics) {
      double distance = 0;
      while (distance < metric.length) {
        final remainingDistance = metric.length - distance;
        final dashLength = remainingDistance < dashWidth ? remainingDistance : dashWidth;
        final extractPath = metric.extractPath(distance, distance + dashLength);
        canvas.drawPath(extractPath, paint);
        distance += dashWidth + dashSpace;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}